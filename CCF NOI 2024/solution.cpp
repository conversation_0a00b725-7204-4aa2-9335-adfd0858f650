#include <iostream>
#include <vector>
#include <set>
#include <map>
#include <algorithm>
using namespace std;

// 检查两个集合序列是否等价（暴力法，适用于小的m）
bool are_equivalent_brute(const vector<vector<int>>& A, const vector<vector<int>>& B, int m) {
    int n = A.size();
    if (n != B.size()) return false;
    
    vector<int> perm(m + 1);
    for (int i = 1; i <= m; i++) perm[i] = i;
    
    do {
        bool valid = true;
        
        for (int i = 0; i < n && valid; i++) {
            multiset<int> setA, setB;
            
            // 应用置换到A[i]
            for (int x : A[i]) {
                setA.insert(perm[x]);
            }
            
            // B[i]保持不变
            for (int x : B[i]) {
                setB.insert(x);
            }
            
            if (setA != setB) {
                valid = false;
            }
        }
        
        if (valid) return true;
        
    } while (next_permutation(perm.begin() + 1, perm.end()));
    
    return false;
}

// 基于模式匹配的高效算法（适用于大的m）
bool are_equivalent_pattern(const vector<vector<int>>& A, const vector<vector<int>>& B, int m) {
    int n = A.size();
    if (n != B.size()) return false;
    
    // 收集所有出现的元素
    set<int> elementsA, elementsB;
    for (const auto& s : A) {
        for (int x : s) elementsA.insert(x);
    }
    for (const auto& s : B) {
        for (int x : s) elementsB.insert(x);
    }
    
    if (elementsA.size() != elementsB.size()) return false;
    
    // 计算每个元素的出现模式
    map<int, vector<int>> patternA, patternB;
    
    for (int elem : elementsA) {
        vector<int> pattern(n, 0);
        for (int i = 0; i < n; i++) {
            for (int x : A[i]) {
                if (x == elem) pattern[i]++;
            }
        }
        patternA[elem] = pattern;
    }
    
    for (int elem : elementsB) {
        vector<int> pattern(n, 0);
        for (int i = 0; i < n; i++) {
            for (int x : B[i]) {
                if (x == elem) pattern[i]++;
            }
        }
        patternB[elem] = pattern;
    }
    
    // 检查模式是否可以一一对应
    multiset<vector<int>> patternsA, patternsB;
    for (auto& p : patternA) patternsA.insert(p.second);
    for (auto& p : patternB) patternsB.insert(p.second);
    
    return patternsA == patternsB;
}

int main() {
    ios_base::sync_with_stdio(false);
    cin.tie(nullptr);
    
    int n, m, q;
    cin >> n >> m >> q;
    
    vector<vector<int>> A(n), B(n);
    
    // 读入序列A
    for (int i = 0; i < n; i++) {
        A[i].resize(3);
        for (int j = 0; j < 3; j++) {
            cin >> A[i][j];
        }
    }
    
    // 读入序列B
    for (int i = 0; i < n; i++) {
        B[i].resize(3);
        for (int j = 0; j < 3; j++) {
            cin >> B[i][j];
        }
    }
    
    // 处理q个询问
    for (int query = 0; query < q; query++) {
        int l, r;
        cin >> l >> r;
        l--; r--; // 转换为0-based索引
        
        // 提取子序列
        vector<vector<int>> subA(A.begin() + l, A.begin() + r + 1);
        vector<vector<int>> subB(B.begin() + l, B.begin() + r + 1);
        
        // 判断等价性
        bool result;
        if (m <= 8) {
            // 对于小的m，使用暴力方法
            result = are_equivalent_brute(subA, subB, m);
        } else {
            // 对于大的m，使用模式匹配方法
            result = are_equivalent_pattern(subA, subB, m);
        }
        
        cout << (result ? "Yes" : "No") << "\n";
    }
    
    return 0;
}
