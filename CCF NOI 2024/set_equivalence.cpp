#include <iostream>
#include <vector>
#include <set>
#include <map>
#include <algorithm>
#include <unordered_map>
#include <unordered_set>
using namespace std;

// 函数声明
bool are_equivalent_brute_force(const vector<vector<int>>& A, const vector<vector<int>>& B, int m);
bool are_equivalent_graph(const vector<vector<int>>& A, const vector<vector<int>>& B, int m);

// 将集合转换为标准形式（排序后的向量）
vector<int> normalize_set(const vector<int>& s) {
    vector<int> result = s;
    sort(result.begin(), result.end());
    return result;
}

// 计算集合的特征值（用于快速比较）
long long compute_signature(const vector<int>& s) {
    vector<int> sorted_s = s;
    sort(sorted_s.begin(), sorted_s.end());
    long long sig = 0;
    for (int i = 0; i < sorted_s.size(); i++) {
        sig = sig * 1000007 + sorted_s[i];
    }
    return sig;
}

// 检查两个集合序列是否等价（优化版本）
bool are_equivalent_fast(const vector<vector<int>>& A, const vector<vector<int>>& B, int m) {
    int n = A.size();
    if (n != B.size()) return false;

    // 计算每个序列的特征模式
    map<vector<long long>, int> patternA, patternB;

    vector<long long> sigA, sigB;
    for (int i = 0; i < n; i++) {
        sigA.push_back(compute_signature(A[i]));
        sigB.push_back(compute_signature(B[i]));
    }

    sort(sigA.begin(), sigA.end());
    sort(sigB.begin(), sigB.end());

    // 如果排序后的特征不同，则不等价
    if (sigA != sigB) return false;

    // 对于小的m，进行完整验证
    if (m <= 8) {
        return are_equivalent_brute_force(A, B, m);
    }

    return true; // 对于大的m，基于特征的检查
}

// 完整的置换检查（仅用于小的m）
bool are_equivalent_brute_force(const vector<vector<int>>& A, const vector<vector<int>>& B, int m) {
    int n = A.size();
    if (n != B.size()) return false;

    vector<int> perm(m + 1);
    for (int i = 1; i <= m; i++) perm[i] = i;

    do {
        bool valid = true;

        for (int i = 0; i < n && valid; i++) {
            vector<int> transformed_A;
            for (int x : A[i]) {
                transformed_A.push_back(perm[x]);
            }

            vector<int> norm_A = normalize_set(transformed_A);
            vector<int> norm_B = normalize_set(B[i]);

            if (norm_A != norm_B) {
                valid = false;
            }
        }

        if (valid) return true;

    } while (next_permutation(perm.begin() + 1, perm.end()));

    return false;
}

// 基于图同构的高效算法
bool are_equivalent_graph(const vector<vector<int>>& A, const vector<vector<int>>& B, int m) {
    int n = A.size();
    if (n != B.size()) return false;

    // 构建二分图：左侧是A中出现的元素，右侧是B中出现的元素
    set<int> elementsA, elementsB;
    for (const auto& s : A) {
        for (int x : s) {
            elementsA.insert(x);
        }
    }
    for (const auto& s : B) {
        for (int x : s) {
            elementsB.insert(x);
        }
    }

    if (elementsA.size() != elementsB.size()) return false;

    // 计算每个元素在各个集合中的出现模式
    map<int, vector<int>> patternA, patternB;

    for (int elem : elementsA) {
        vector<int> pattern;
        for (int i = 0; i < n; i++) {
            int count = 0;
            for (int x : A[i]) {
                if (x == elem) count++;
            }
            pattern.push_back(count);
        }
        patternA[elem] = pattern;
    }

    for (int elem : elementsB) {
        vector<int> pattern;
        for (int i = 0; i < n; i++) {
            int count = 0;
            for (int x : B[i]) {
                if (x == elem) count++;
            }
            pattern.push_back(count);
        }
        patternB[elem] = pattern;
    }

    // 检查是否存在模式的一一对应
    multiset<vector<int>> patternsA, patternsB;
    for (auto& p : patternA) patternsA.insert(p.second);
    for (auto& p : patternB) patternsB.insert(p.second);

    return patternsA == patternsB;
}

int main() {
    ios_base::sync_with_stdio(false);
    cin.tie(nullptr);
    
    int n, m, q;
    cin >> n >> m >> q;
    
    vector<vector<int>> A(n), B(n);
    
    // 读入序列A
    for (int i = 0; i < n; i++) {
        A[i].resize(3);
        for (int j = 0; j < 3; j++) {
            cin >> A[i][j];
        }
    }
    
    // 读入序列B
    for (int i = 0; i < n; i++) {
        B[i].resize(3);
        for (int j = 0; j < 3; j++) {
            cin >> B[i][j];
        }
    }
    
    // 处理q个询问
    for (int query = 0; query < q; query++) {
        int l, r;
        cin >> l >> r;
        l--; r--; // 转换为0-based索引
        
        // 提取子序列
        vector<vector<int>> subA(A.begin() + l, A.begin() + r + 1);
        vector<vector<int>> subB(B.begin() + l, B.begin() + r + 1);
        
        // 判断等价性
        if (m <= 8) {
            // 对于小的m，使用完整的置换枚举
            cout << (are_equivalent_brute_force(subA, subB, m) ? "Yes" : "No") << "\n";
        } else {
            // 对于大的m，使用基于图的方法
            cout << (are_equivalent_graph(subA, subB, m) ? "Yes" : "No") << "\n";
        }
    }
    
    return 0;
}
