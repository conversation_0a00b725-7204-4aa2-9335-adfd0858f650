
#include "richest.h"
#include <vector>
#include <numeric>

// Forward declaration of the function provided by the grader
std::vector<int> ask(std::vector<int> a, std::vector<int> b);

int richest(int N, int T, int S) {
    if (N <= 1) {
        return 0;
    }

    // Strategy for Test Case 1 (e.g., N=1000, T=1)
    // With only one request allowed, we compare all possible pairs.
    // The richest person will win N-1 comparisons.
    if (T == 1) {
        std::vector<int> group_a;
        std::vector<int> group_b;
        
        // Generate all unique pairs of customers
        for (int i = 0; i < N; ++i) {
            for (int j = i + 1; j < N; ++j) {
                group_a.push_back(i);
                group_b.push_back(j);
            }
        }

        std::vector<int> winners = ask(group_a, group_b);

        // Count the number of wins for each customer
        std::vector<int> win_counts(N, 0);
        for (int winner : winners) {
            win_counts[winner]++;
        }

        // Find the customer who won against everyone else (N-1 wins)
        for (int i = 0; i < N; ++i) {
            if (win_counts[i] == N - 1) {
                return i;
            }
        }
    } 
    // Strategy for Test Case 2 (e.g., N=1,000,000, T=20)
    // Use a knockout tournament style to find the winner.
    else {
        std::vector<int> candidates(N);
        std::iota(candidates.begin(), candidates.end(), 0); // Fill with 0, 1, ..., N-1

        while (candidates.size() > 1) {
            std::vector<int> group_a;
            std::vector<int> group_b;
            std::vector<int> next_round_candidates;

            // Pair up current candidates for comparison
            for (size_t i = 0; i < candidates.size() / 2; ++i) {
                group_a.push_back(candidates[2 * i]);
                group_b.push_back(candidates[2 * i + 1]);
            }

            // Get the winners from the comparisons
            if (!group_a.empty()) {
                next_round_candidates = ask(group_a, group_b);
            }

            // If there was an odd number of candidates, the leftover one
            // automatically advances to the next round.
            if (candidates.size() % 2 != 0) {
                next_round_candidates.push_back(candidates.back());
            }
            
            candidates = next_round_candidates;
        }

        return candidates[0];
    }

    // Should not be reached given the problem constraints
    return -1; 
}
