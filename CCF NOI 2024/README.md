# 集合序列等价性判断

## 题目理解

这是一个关于集合序列等价性判断的问题：

1. **基本集合**：大小为3，元素在1~m范围内
2. **集合序列**：由基本集合构成的序列
3. **置换函数**：fp(S) = {p[x] | x ∈ S}，将集合S中每个元素x替换为p[x]
4. **等价性**：两个序列A和B等价，当且仅当存在置换p使得A经过置换后得到B

## 解决方案

### 算法思路

1. **小规模数据（m ≤ 8）**：使用暴力枚举所有可能的置换
   - 时间复杂度：O(m! × n × 3)
   - 对于m ≤ 8，8! = 40320，完全可以接受

2. **大规模数据（m > 8）**：使用基于模式匹配的算法
   - 计算每个元素在各个集合中的出现模式
   - 检查两个序列的模式集合是否相同
   - 时间复杂度：O(n × m × 3)

### 核心观察

两个集合序列等价的充要条件是：
- 存在一个置换，使得对应位置的集合在置换后相等
- 等价地，两个序列中每个元素的"出现模式"可以一一对应

### 代码实现

```cpp
// 暴力方法：枚举所有置换
bool are_equivalent_brute(const vector<vector<int>>& A, const vector<vector<int>>& B, int m);

// 模式匹配方法：基于元素出现模式
bool are_equivalent_pattern(const vector<vector<int>>& A, const vector<vector<int>>& B, int m);
```

## 时间复杂度分析

- **预处理**：O(n)
- **每次查询**：
  - m ≤ 8：O(m! × (r-l+1) × 3)
  - m > 8：O((r-l+1) × m × 3)

## 空间复杂度

O(n × 3 + m) = O(n + m)

## 测试

使用提供的样例数据进行测试，输出结果与期望一致。

## 优化点

1. 对于小的m使用暴力方法确保正确性
2. 对于大的m使用模式匹配方法确保效率
3. 使用multiset进行集合比较，自动处理重复元素
4. 预先计算元素出现模式，避免重复计算
